document.addEventListener('DOMContentLoaded', async () => {
  console.log('DOM Content Loaded');
  
  // Handle back button click
  document.getElementById('backButton').addEventListener('click', () => {
    window.location.href = 'popup.html';
  });

  const loadingDiv = document.getElementById('loading');
  const errorDiv = document.getElementById('error');
  const responseDiv = document.getElementById('response');
  const pageTitleDiv = document.getElementById('pageTitle');
  const pageUrlDiv = document.getElementById('pageUrl');
  const pageContentDiv = document.getElementById('pageContent');
  const saveStatusDiv = document.getElementById('saveStatus');
  const saveButton = document.getElementById('saveButton');

  // 检查按钮是否存在
  if (!saveButton) {
    console.error('Save button not found in the DOM!');
    return;
  }
  console.log('Save button found in DOM');

  let currentPageData = {
    title: '',
    url: '',
    content: '',
    response: ''
  };

  // 将保存按钮的事件监听器移到外部
  saveButton.addEventListener('click', async () => {
    // Check if user is logged in
    if (!window.isUserLoggedIn) {
      alert('Please login to use this feature');
      return;
    }
    
    try {
      console.log('Save button clicked');
      console.log('Current page data:', currentPageData);
      
      saveStatusDiv.textContent = 'Saving to MongoDB...';
      saveStatusDiv.className = '';
      saveStatusDiv.style.display = 'block';

      // 验证数据
      if (!currentPageData.title || !currentPageData.url || !currentPageData.content || !currentPageData.response) {
        console.log('Data validation failed:', {
          title: !!currentPageData.title,
          url: !!currentPageData.url,
          content: !!currentPageData.content,
          response: !!currentPageData.response
        });
        throw new Error('Missing required data. Please make sure all fields are present.');
      }

      console.log('Sending save request...');
      // Save data using background script
      const response = await chrome.runtime.sendMessage({
        action: 'saveToMongoDB',
        data: {
          title: currentPageData.title,
          url: currentPageData.url,
          content: currentPageData.content,
          response: currentPageData.response,
          timestamp: new Date().toISOString()
        }
      });
      console.log('Save response:', response);

      if (response.success) {
        saveStatusDiv.textContent = 'Successfully saved to MongoDB!';
        saveStatusDiv.className = 'success';
      } else {
        throw new Error(response.error || 'Failed to save to MongoDB');
      }
    } catch (error) {
      console.error('Save error:', error);
      saveStatusDiv.textContent = `Failed to save: ${error.message}`;
      saveStatusDiv.className = 'error';
    }
  });

  try {
    // Get current active tab
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    // Display page title and URL immediately
    currentPageData.title = activeTab.title;
    currentPageData.url = activeTab.url;
    pageTitleDiv.textContent = currentPageData.title;
    pageUrlDiv.textContent = currentPageData.url;
    
    // Get page content
    const pageContent = await new Promise((resolve, reject) => {
      chrome.tabs.sendMessage(activeTab.id, { action: 'getPageContent' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error('Unable to get page content. Please make sure the page is fully loaded.'));
          return;
        }
        
        if (!response || !response.success) {
          reject(new Error(response?.error || 'Failed to get page content'));
          return;
        }
        
        resolve(response.content);
      });
    });

    // Display page content immediately
    currentPageData.content = pageContent;
    pageContentDiv.textContent = pageContent;

    // Show loading state for API call
    loadingDiv.style.display = 'block';
    errorDiv.style.display = 'none';
    responseDiv.textContent = '';

    // Get settings from storage
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get(['token', 'baseUrl', 'model', 'prompt', 'mongoUrl'], resolve);
    });

    if (!settings.token || !settings.baseUrl || !settings.model || !settings.prompt) {
      throw new Error('Please configure all required settings (Token, Base URL, Model, and Prompt) in the Settings page.');
    }

    // Clean and validate baseUrl
    let baseUrl = settings.baseUrl.trim();
    if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
      baseUrl = 'https://' + baseUrl;
    }
    baseUrl = baseUrl.replace(/\/$/, '');

    // Prepare API request
    const requestBody = {
      model: settings.model,
      messages: [{
        role: 'user',
        content: `Please analyze and summarize the following text:\n\n${pageContent}`
      }],
      stream: false,
      max_tokens: 512,
      enable_thinking: false,
      thinking_budget: 4096,
      min_p: 0.05,
      stop: null,
      temperature: 0.7,
      top_p: 0.7,
      top_k: 50,
      frequency_penalty: 0.5,
      n: 1,
      response_format: { type: 'text' }
    };

    const options = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${settings.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    };

    // Make API call
    const response = await fetch(`${baseUrl}/chat/completions`, options);
    const rawResponse = await response.text();
    
    let data;
    try {
      data = JSON.parse(rawResponse);
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError);
      throw new Error(`Failed to parse API response: ${parseError.message}\nRaw response: ${rawResponse.substring(0, 200)}...`);
    }

    if (!response.ok) {
      throw new Error(data.error?.message || `API request failed with status ${response.status}`);
    }

    // Display response
    const responseContent = data.choices?.[0]?.message?.content;
    if (!responseContent) {
      throw new Error('No response content in API response');
    }

    // Save response to currentPageData
    currentPageData.response = responseContent;

    // Format and display the response
    responseDiv.innerHTML = `
      <div style="white-space: pre-wrap; word-wrap: break-word;">
        ${responseContent}
      </div>
    `;

  } catch (error) {
    console.error('Error:', error);
    errorDiv.textContent = error.message;
    errorDiv.style.display = 'block';
  } finally {
    loadingDiv.style.display = 'none';
  }
}); 
