let isDebug = false;
document.addEventListener('DOMContentLoaded', async () => {
  // Check authentication status and update UI
  checkAuthAndUpdateUI();
  
  // Handle back button click
  document.getElementById('backButton').addEventListener('click', () => {
    window.location.href = 'popup.html';
  });
  
  // Handle Add to Bookmark button click
  document.getElementById('addToBookmark').addEventListener('click', async () => { // Made this async
    try {
      //alert("Starting to add bookmarks...");
      
      // 获取当前窗口的标签页
      const currentWindow = await new Promise(resolve => {
        chrome.windows.getCurrent({populate: true}, resolve);
      });
      const tabs = currentWindow.tabs || [];
      //alert(`Number of tabs in current window: ${tabs.length}`);
      
      // 获取标签页的分类信息
      const tabCategories = {};
      const tabTags = {};
      
      // 收集所有标签页的分类和标签信息
      // This part needs to be async if chrome.storage.sync.get is used with await or returns a Promise
      // For simplicity, assuming it populates tabTags correctly before processTabs is called
      // or that processTabs handles the async nature of tabTags if needed.
      for (const tab of tabs) {
        const categorySelect = document.querySelector(`select[data-tab-id="${tab.id}"]`);
        if (categorySelect) {
          tabCategories[tab.id] = categorySelect.value;
        }
        // Assuming tabTags are fetched and populated synchronously for this example
        // or handled asynchronously before calling processTabs
        const storageResult = await new Promise(resolve => chrome.storage.sync.get(['tabTags'], resolve));
        tabTags[tab.id] = (storageResult.tabTags && storageResult.tabTags[tab.id]) || [];
      }
      
      // 创建或获取Catai根文件夹
      // Refactored to use async/await for clarity
      let cataiFolder = await new Promise((resolve, reject) => {
        chrome.bookmarks.search({title: 'Catai'}, results => {
          if (results.length > 0) {
            resolve(results[0]);
          } else {
            chrome.bookmarks.create({title: 'Catai'}, folder => {
              if (chrome.runtime.lastError) {
                console.error("Error creating Catai folder:", chrome.runtime.lastError.message);
                reject(chrome.runtime.lastError);
              } else {
                resolve(folder);
              }
            });
          }
        });
      });

      if (!cataiFolder) {
        alert("Failed to create or find Catai folder. Aborting.");
        return;
      }
        
      // 处理所有标签页
      await processTabs(tabs, tabCategories, tabTags, cataiFolder); // Added await here

    } catch (error) {
      alert(`Error adding bookmarks: ${error.message}`);
      console.error("Error in addToBookmark click handler:", error);
    }
  });
  
  // 处理标签页并添加到书签
  async function processTabs(tabs, tabCategories, tabTags, cataiFolder) { // Made processTabs async
    //alert("Categories found: " + JSON.stringify(tabCategories));
    
    // 按分类分组标签页
    const tabsByCategory = {};
    
    tabs.forEach(tab => {
      const category = tabCategories[tab.id] || "Uncategorized";
      if (!tabsByCategory[category]) {
        tabsByCategory[category] = [];
      }
      tabsByCategory[category].push({
        tab: tab,
        tags: tabTags[tab.id] || []
      });
    });
    
    // 处理每个分类
    let processedCount = 0;
    const categories = Object.keys(tabsByCategory);
    const totalCategories = categories.length;
    
    for (const category of categories) { // Changed from forEach to for...of loop
      if (category && category !== "Uncategorized") {
        try {
            let categoryFolder = await new Promise((resolve, reject) => {
                chrome.bookmarks.search({ title: category }, results => {
                    const categoryInCatai = results.find(r => r.parentId === cataiFolder.id);
                    if (categoryInCatai) {
                        resolve(categoryInCatai);
                    } else {
                        chrome.bookmarks.create({ parentId: cataiFolder.id, title: category }, folder => {
                            if (chrome.runtime.lastError) {
                                console.error(`Error creating category folder '${category}':`, chrome.runtime.lastError.message);
                                reject(chrome.runtime.lastError);
                            } else { 
                                //if( isDebug )
                                  alert(`Created new category folder: ${category}`);
                                resolve(folder);
                            }
                        });
                    }
                });
            });

            if (categoryFolder) {
                await addTabsToFolder(tabsByCategory[category], categoryFolder); // Assuming addTabsToFolder is also async
            }
        } catch (error) {
            alert(`Error processing category '${category}': ${error.message}`);
            console.error(`Error processing category '${category}':`, error);
        }
      } else { // Handling "Uncategorized"
        let dateFolder;
        try {   
          const historyFolder =  await findOrCreateFolder('History', cataiFolder.id);
          
          if (!historyFolder) {
              console.error("cata.js: Failed to find or create 'History' folder.");
              alert("错误：无法访问 'History' 文件夹。未分类标签页将不会保存到日期文件夹。");
          } else {
            dateFolder = await createDateFolder(historyFolder.id); 

            if (dateFolder) { 
                strDate = new Date().toISOString()
                
                if( isDebug ) {
                  alert(`Date Folder Created: ${strDate}`);
                  alert(`Date Folder Created: ${dateFolder.title || dateFolder.id}`);
                }} else {
                alert("Error：no date Folder is created。");
                console.error("cata.js: dateFolder named null or undefined。将跳过保存未分类标签页。");
                // Skip adding to this folder if not created
            }
          }
        } catch (error) {
          alert(`处理未分类标签页时出错: ${error.message}`);
          console.error("Error processing uncategorized tabs:", error);
        }

        if (dateFolder) { 
            await addTabsToFolder(tabsByCategory[category], dateFolder); // Assuming addTabsToFolder is also async
        } else {
            alert("由于日期文件夹无效，未分类标签页未添加。");
        }
      }
      processedCount++;
    } // End of for...of loop

    if (processedCount === totalCategories) {
      alert("Done! All tabs have been categorized and saved."); // This alert might still be tricky if addTabsToFolder has internal async ops not awaited fully by its caller
    }
  }
  
  // Ensure addTabsToFolder is also async if it performs async operations like chrome.bookmarks.create
  async function addTabsToFolder(tabItems, folder) {
    tabItems.forEach(item => {
      const {tab, tags} = item;
      
      if (tags && tags.length > 0) {
        // 如果有标签，为每个标签创建子文件夹
        tags.forEach(tag => {
          // 查找或创建标签文件夹
          chrome.bookmarks.search({title: tag}, results => {
            // 在当前分类文件夹下查找标签文件夹
            const tagInFolder = results.filter(r => 
              r.parentId === folder.id
            );
            
            if (tagInFolder.length > 0) {
              // 使用现有的标签文件夹
              chrome.bookmarks.create({
                parentId: tagInFolder[0].id,
                title: tab.title,
                url: tab.url
              });
            } else {
              // 创建新的标签文件夹
              chrome.bookmarks.create({
                parentId: folder.id,
                title: tag
              }, tagFolder => {
                // 在标签文件夹中添加书签
                chrome.bookmarks.create({
                  parentId: tagFolder.id,
                  title: tab.title,
                  url: tab.url
                });
              });
            }
          });
        });
      } else {
        // 没有标签，直接添加到分类文件夹 ， 保存到 History
        chrome.bookmarks.create({
          parentId: folder.id,
          title: tab.title,
          url: tab.url
        });
      }
    });
  }
  
  async function findOrCreateFolder(title, parentId) {
    const existing = await chrome.bookmarks.search({title});
    const folder = existing.find(f => f.parentId === parentId);
    return folder || await chrome.bookmarks.create({title, parentId});
  }
  async function createDateFolder(parentId) {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // getMonth() 是 0-indexed, 所以 +1
    const day = String(today.getDate()).padStart(2, '0');
    const localDateString = `${year}-${month}-${day}`;
    
    // alert(`Using local date for folder: ${localDateString}`); // Optional: for debugging
    return findOrCreateFolder(localDateString, parentId);
  }

  // Handle load categories button click
  document.getElementById('loadCategories').addEventListener('click', async () => {
    // Check if user is logged in
    const authResult = await new Promise(resolve => {
      chrome.storage.sync.get(['authUser'], resolve);
    });
    
    if (!authResult.authUser || !authResult.authUser.loggedIn) {
      alert('Please login to use this feature');
      return;
    }
    
    try {
      console.log('Fetching categories from MongoDB...');
      
      // 获取当前profile名称
      const profileResult = await new Promise(resolve => {
        chrome.storage.sync.get(['llmKey', 'nodeJsServer'], resolve);
      });
      const profileName = profileResult.llmKey || 'test';
      const serverUrl = profileResult.nodeJsServer || 'http://localhost:3000';
      
      const response = await fetch(`${serverUrl}/get-categories?profileName=${profileName}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      console.log('Response status:', response.status);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.categories && result.categories.length > 0) {
          // 获取所有下拉框
          const categorySelects = document.querySelectorAll('select[data-tab-id]');
          
          // 保存到本地存储
          chrome.storage.sync.get(['categories'], (storageResult) => {
            let categories = storageResult.categories || [];
            
            // 合并MongoDB中的类别和本地存储的类别
            result.categories.forEach(category => {
              if (!categories.includes(category)) {
                categories.push(category);
              }
            });
            
            // 更新本地存储
            chrome.storage.sync.set({ categories }, () => {
              // 更新所有下拉框
              categorySelects.forEach(select => {
                // 保存当前选中的值
                const currentValue = select.value;
                
                // 清空除了第一个选项外的所有选项
                while (select.options.length > 1) {
                  select.remove(1);
                }
                
                // 添加所有类别
                categories.forEach(category => {
                  const option = document.createElement('option');
                  option.value = category;
                  option.textContent = category;
                  select.appendChild(option);
                });
                
                // 恢复之前选中的值
                if (currentValue) {
                  select.value = currentValue;
                }
              });
              
              alert(`Successfully loaded ${result.categories.length} categories from MongoDB!`);
            });
          });
        } else {
          alert('No categories found in MongoDB.');
        }
      } else {
        throw new Error('Failed to load categories');
      }
    } catch (error) {
      console.error('Load categories error:', error);
      alert(`Failed to load categories: ${error.message}`);
    }
  });

  // Handle Load from Catai button click
  document.getElementById('loadFromCatai').addEventListener('click', async function() {
    try {
      if( isDebug )
        alert("Debug: Starting to load from Catai bookmarks");
      
      // Find the Catai main folder
      const cataiMainFolderResults = await new Promise(resolve => {
        chrome.bookmarks.search({title: 'Catai'}, resolve);
      });
      
      if (!cataiMainFolderResults || cataiMainFolderResults.length === 0) {
        alert('Catai bookmark folder not found. Please create it first.');
        return;
      }
      if( isDebug ) {
         alert(`Debug: Found Catai folder with ID: ${cataiMainFolderResults[0].id}`);
      }
      // Get all subfolders in Catai
      const cataiFolders = await new Promise(resolve => {
        chrome.bookmarks.getChildren(cataiMainFolderResults[0].id, children => {
          resolve(children.filter(child => !child.url)); // Filter out bookmarks, keep folders
        });
      });
      
      // Exclude specific directories
      const excludedFolders = ['History', 'Urgency', 'Importance', 'Reminder'];
      const validFolders = cataiFolders.filter(folder => 
        !excludedFolders.includes(folder.title)
      );
      
      if (validFolders.length === 0) {
        alert('No valid category folders found in Catai bookmarks.');
        return;
      }
      
      if( isDebug )
        alert(`Debug: Found ${validFolders.length} valid category folders`);
      
      // Extract category names
      const categoryNames = validFolders.map(folder => folder.title);
      
      // Get existing categories from storage
      const storageResult = await new Promise(resolve => {
        chrome.storage.sync.get(['categories'], resolve);
      });
      
      let categories = storageResult.categories || [];
      let newCategoriesCount = 0;
      
      // Add new categories
      categoryNames.forEach(category => {
        if (!categories.includes(category)) {
          categories.push(category);
          newCategoriesCount++;
        }
      });
      
      // Save updated categories to storage
      await new Promise(resolve => {
        chrome.storage.sync.set({ categories }, resolve);
      });
      
      // Get current tabs
      const currentWindow = await new Promise(resolve => {
        chrome.windows.getCurrent({populate: true}, resolve);
      });
      const tabs = currentWindow.tabs || [];
      
      if( isDebug )
        alert(`Debug: Processing ${tabs.length} tabs to match with bookmarks`);
      
      // Create a map to store category assignments for each tab
      const tabCategoryMap = {};
      const tabTagsMap = {};
      
      // For each valid category folder, get all bookmarks and match with current tabs
      let matchedTabsCount = 0;
      
      for (const folder of validFolders) {
        const categoryName = folder.title;
        
        // Get all bookmarks in this category folder
        const bookmarksInCategory = await getAllBookmarksInFolder(folder.id);
        
        // Match bookmarks with current tabs
        for (const tab of tabs) {
          for (const bookmark of bookmarksInCategory) {
            // Check if title matches and URL is similar
            if (bookmark.title === tab.title && isSimilarUrl(bookmark.url, tab.url)) {
              tabCategoryMap[tab.id] = categoryName;
              
              // If bookmark is in a subfolder, that subfolder name is a tag
              if (bookmark.parentFolder && bookmark.parentFolder !== categoryName) {
                if (!tabTagsMap[tab.id]) {
                  tabTagsMap[tab.id] = [];
                }
                if (!tabTagsMap[tab.id].includes(bookmark.parentFolder)) {
                  tabTagsMap[tab.id].push(bookmark.parentFolder);
                }
              }
              
              matchedTabsCount++;
              break; // Found a match for this tab, move to next tab
            }
          }
        }
      }
      
      if( isDebug )
        alert(`Debug: Matched ${matchedTabsCount} tabs with bookmarks`);
      
      // Update UI with category assignments
      if (Object.keys(tabCategoryMap).length > 0) {
        // Update category dropdowns
        for (const tabId in tabCategoryMap) {
          const categorySelect = document.querySelector(`select[data-tab-id="${tabId}"]`);
          if (categorySelect) {
            categorySelect.value = tabCategoryMap[tabId];
          }
        }
        
        // Update tags
        if (Object.keys(tabTagsMap).length > 0) {
          // Get existing tags from storage
          const tagStorageResult = await new Promise(resolve => {
            chrome.storage.sync.get(['tabTags'], result => resolve(result.tabTags || {}));
          });
          
          let tabTags = tagStorageResult || {};
          
          // Merge new tags with existing ones
          for (const tabId in tabTagsMap) {
            if (!tabTags[tabId]) {
              tabTags[tabId] = [];
            }
            
            tabTagsMap[tabId].forEach(tag => {
              if (!tabTags[tabId].includes(tag)) {
                tabTags[tabId].push(tag);
              }
            });
          }
          
          // Save updated tags to storage
          await new Promise(resolve => {
            chrome.storage.sync.set({ tabTags }, resolve);
          });
          
          // Refresh the page to show updated tags
          // This is a simple approach - a more sophisticated one would update the UI directly
          location.reload();
        }
      }
      
      // Update all category dropdowns
      const categorySelects = document.querySelectorAll('select[data-tab-id]');
      categorySelects.forEach(select => {
        // Save current selected value
        const currentValue = select.value;
        
        // Clear all options except the first one
        while (select.options.length > 1) {
          select.remove(1);
        }
        
        // Add all categories
        categories.forEach(category => {
          const option = document.createElement('option');
          option.value = category;
          option.textContent = category;
          select.appendChild(option);
        });
        
        // Restore previously selected value
        if (currentValue) {
          select.value = currentValue;
        }
      });
      
      alert(`Successfully loaded ${categoryNames.length} categories from Catai bookmarks! (${newCategoriesCount} new categories added)\nMatched ${matchedTabsCount} tabs with bookmarks.`);
      
    } catch (error) {
      console.error('Error loading from Catai bookmarks:', error);
      alert(`Failed to load from Catai bookmarks: ${error.message}`);
    }
  });
  
  // Helper function to get all bookmarks in a folder and its subfolders
  async function getAllBookmarksInFolder(folderId) {
    const bookmarks = [];
    
    // Get all items in this folder
    const items = await new Promise(resolve => {
      chrome.bookmarks.getChildren(folderId, resolve);
    });
    
    for (const item of items) {
      if (item.url) {
        // This is a bookmark
        bookmarks.push({
          title: item.title,
          url: item.url,
          parentId: item.parentId
        });
      } else {
        // This is a subfolder, get its name
        const folder = await new Promise(resolve => {
          chrome.bookmarks.get(item.id, results => resolve(results[0]));
        });
        
        // Get bookmarks in this subfolder
        const subfolderBookmarks = await new Promise(resolve => {
          chrome.bookmarks.getChildren(item.id, resolve);
        });
        
        // Add each bookmark with its parent folder name
        for (const bookmark of subfolderBookmarks) {
          if (bookmark.url) {
            bookmarks.push({
              title: bookmark.title,
              url: bookmark.url,
              parentId: bookmark.parentId,
              parentFolder: folder.title
            });
          }
        }
      }
    }
    
    return bookmarks;
  }
  
  // Helper function to check if two URLs are similar
  function isSimilarUrl(url1, url2) {
    // Simple exact match
    if (url1 === url2) return true;
    
    try {
      // Parse URLs to compare their components
      const parsed1 = new URL(url1);
      const parsed2 = new URL(url2);
      
      // Compare hostname and pathname
      if (parsed1.hostname === parsed2.hostname && 
          parsed1.pathname === parsed2.pathname) {
        return true;
      }
    } catch (e) {
      // If URL parsing fails, fall back to simple comparison
      return url1 === url2;
    }
    
    return false;
  }

  // 首先从MongoDB加载所有数据
  let mongoData = [];
  try {
    // Check if user is logged in
    const authResult = await new Promise(resolve => {
      chrome.storage.sync.get(['authUser'], resolve);
    });
    
    if (authResult.authUser && authResult.authUser.loggedIn) {
      // 获取当前profile名称
      const profileResult = await new Promise(resolve => {
        chrome.storage.sync.get(['llmKey', 'nodeJsServer'], resolve);
      });
      const profileName = profileResult.llmKey || 'test';
      const serverUrl = profileResult.nodeJsServer || 'http://localhost:3000';
      
      const response = await fetch(`${serverUrl}/get-cata-data?profileName=${profileName}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          mongoData = result.data || [];
          console.log('Loaded data from MongoDB:', mongoData);
        }
      }
    } else {
      console.log('User not logged in, skipping MongoDB data loading');
    }
  } catch (error) {
    console.error('Error loading data from MongoDB:', error);
  }

  // Handle save to MongoDB button click
  document.getElementById('saveToMongo').addEventListener('click', async () => {
    // Check if user is logged in
    const authResult = await new Promise(resolve => {
      chrome.storage.sync.get(['authUser'], resolve);
    });
    
    if (!authResult.authUser || !authResult.authUser.loggedIn) {
      alert('Please login to use this feature');
      return;
    }
    
    try {
      // 原来是查询所有标签页
      // const tabs = await new Promise(resolve => {
      //   chrome.tabs.query({}, resolve);
      // });
      // 现在只查询当前窗口的标签页
      const currentWindow = await new Promise(resolve => {
        chrome.windows.getCurrent({populate: true}, resolve);
      });
      const tabs = currentWindow.tabs || [];
      
      const tabTags = await new Promise(resolve => {
        chrome.storage.sync.get(['tabTags'], result => resolve(result.tabTags || {}));
      });
      
      const dataToSave = tabs.map(tab => ({
        title: tab.title,
        url: tab.url,
        tags: tabTags[tab.id] || [],
        timestamp: new Date().toISOString(),
        category: document.querySelector(`select[data-tab-id="${tab.id}"]`)?.value || '',
        urgency: document.querySelector(`select[data-urgency-tab-id="${tab.id}"]`)?.value || '2',
        importance: document.querySelector(`select[data-importance-tab-id="${tab.id}"]`)?.value || '2',
        reminder: document.querySelector(`input[data-reminder-tab-id="${tab.id}"]`)?.value || ''
      }));

      // 在保存前获取现有数据以计算新增和更新的记录数
      let existingData = [];
      try {
        // 使用之前获取的profileName和serverUrl
        const existingResponse = await fetch(`${serverUrl}/get-cata-data?profileName=${profileName}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
        
        if (existingResponse.ok) {
          const existingResult = await existingResponse.json();
          existingData = existingResult.data || [];
        }
      } catch (error) {
        console.error('Error fetching existing data:', error);
      }
      
      // 计算新增和更新的记录数
      let addedCount = 0;
      let updatedCount = 0;
      
      dataToSave.forEach(item => {
        const existingItem = existingData.find(existing => 
          existing.url === item.url
        );
        
        if (existingItem) {
          // 检查是否有实际变化（标签、分类、紧急度、重要性或提醒日期）
          const tagsChanged = !arraysEqual(existingItem.tags || [], item.tags || []);
          const categoryChanged = existingItem.category !== item.category;
          
          // 确保将字符串格式进行比较，因为从DOM获取的值可能是字符串
          const existingUrgency = String(existingItem.urgency || '2');
          const newUrgency = String(item.urgency || '2');
          const urgencyChanged = existingUrgency !== newUrgency;
          
          const existingImportance = String(existingItem.importance || '2');
          const newImportance = String(item.importance || '2');
          const importanceChanged = existingImportance !== newImportance;
          
          const existingReminder = String(existingItem.reminder || '');
          const newReminder = String(item.reminder || '');
          const reminderChanged = existingReminder !== newReminder;
          
          // 调试输出，帮助诊断问题
          console.log(`Item: ${item.title}`, {
            urgency: { existing: existingUrgency, new: newUrgency, changed: urgencyChanged },
            importance: { existing: existingImportance, new: newImportance, changed: importanceChanged },
            reminder: { existing: existingReminder, new: newReminder, changed: reminderChanged }
          });
          
          if (tagsChanged || categoryChanged || urgencyChanged || importanceChanged || reminderChanged) {
            updatedCount++;
          } else {
            // 即使没有变化，也算作更新，因为我们总是更新时间戳
            updatedCount++;
          }
        } else {
          addedCount++;
        }
      });
      
      // 辅助函数：比较两个数组是否相等
      function arraysEqual(a, b) {
        if (a.length !== b.length) return false;
        const sortedA = [...a].sort();
        const sortedB = [...b].sort();
        return sortedA.every((val, i) => val === sortedB[i]);
      }
      
      // 获取当前profile名称
      const profileResult = await new Promise(resolve => {
        chrome.storage.sync.get(['llmKey', 'nodeJsServer'], resolve);
      });
      const profileName = profileResult.llmKey || 'test';
      const serverUrl = profileResult.nodeJsServer || 'http://localhost:3000';
      
      // 批量保存到MongoDB
      const response = await fetch(`${serverUrl}/save-cata-batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          collection: 'CataPage',
          documents: dataToSave,
          profileName: profileName
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save batch');
      }
      
      alert(`Successfully saved to MongoDB!\nAdded: ${addedCount} records\nUpdated: ${updatedCount} records`);
    } catch (error) {
      console.error('Save error:', error);
      alert(`Failed to save: ${error.message}`);
    }
  });

  // Load tabs and their tags - only from current window
  chrome.windows.getCurrent({populate: true}, (currentWindow) => {
    const tabs = currentWindow.tabs || [];
    const tabList = document.getElementById('tabList');
    
    // Check authentication status
    chrome.storage.sync.get(['authUser'], (authResult) => {
      const isLoggedIn = authResult.authUser && authResult.authUser.loggedIn;
      
      // Store login state in a global variable for other functions to access
      window.isUserLoggedIn = isLoggedIn;
      
      // Load saved tags
      chrome.storage.sync.get(['tabTags'], (result) => {
        const tabTags = result.tabTags || {};
        
        tabs.forEach((tab) => {
          const tabItem = document.createElement('div');
          tabItem.className = 'tab-item';
          
          const titleDiv = document.createElement('div');
          titleDiv.className = 'tab-title';
          titleDiv.textContent = tab.title;
          
          const urlDiv = document.createElement('div');
          urlDiv.className = 'tab-url';
          urlDiv.textContent = tab.url;
          
          const tagContainer = document.createElement('div');
          tagContainer.className = 'tag-container';
          
          // 查找MongoDB中是否已存在该URL的记录
          const existingData = mongoData.find(item => item.url === tab.url);
          
          // 如果MongoDB中存在记录，使用MongoDB中的tags，否则使用本地存储的tags
          let tags = [];
          if (existingData && existingData.tags && existingData.tags.length > 0) {
            tags = existingData.tags;
            // 同步到本地存储
            tabTags[tab.id] = tags;
          } else {
            tags = tabTags[tab.id] || [];
          }
          
          // Display existing tags
          tags.forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = 'tag-display';
            tagSpan.textContent = tag;
            tagContainer.appendChild(tagSpan);
          });
          
          const inputGroup = document.createElement('div');
          inputGroup.style.marginTop = '10px';
          
          // Add category select dropdown
          const categorySelect = document.createElement('select');
          categorySelect.className = 'tag-input';
          categorySelect.setAttribute('data-tab-id', tab.id);
          
          // Add new category input
          const newCategoryInput = document.createElement('input');
          newCategoryInput.type = 'text';
          newCategoryInput.className = 'tag-input';
          newCategoryInput.placeholder = 'New category name';
          
          // Add category button
          const addCategoryButton = document.createElement('button');
          addCategoryButton.className = 'save-button';
          addCategoryButton.textContent = 'Add Category';
          
          // Add tag input
          const tagInput = document.createElement('input');
          tagInput.type = 'text';
          tagInput.className = 'tag-input';
          tagInput.placeholder = 'Add a tag...';
          
          // Add tag button
          const saveButton = document.createElement('button');
          saveButton.className = 'save-button';
          saveButton.textContent = 'Add Tag';
          
          saveButton.addEventListener('click', () => {
            const newTag = tagInput.value.trim();
            if (newTag) {
              const currentTags = tabTags[tab.id] || [];
              if (!currentTags.includes(newTag)) {
                currentTags.push(newTag);
                tabTags[tab.id] = currentTags;
                
                // Save to storage
                chrome.storage.sync.set({ tabTags }, () => {
                  // Add new tag to display
                  const tagSpan = document.createElement('span');
                  tagSpan.className = 'tag-display';
                  tagSpan.textContent = newTag;
                  tagContainer.appendChild(tagSpan);
                  
                  // Clear input
                  tagInput.value = '';
                });
              }
            }
          });
          
          // 添加标签建议按钮
          const suggestButton = document.createElement('button');
          suggestButton.className = 'suggest-tags-btn';
          suggestButton.textContent = 'Suggest Tags';
          
          // 添加Show按钮，点击后激活对应标签页
          const showButton = document.createElement('button');
          showButton.className = 'show-tab-btn';
          showButton.textContent = 'Show';
          showButton.style.marginLeft = '5px';
          showButton.style.backgroundColor = '#FF9800';
          showButton.style.color = 'white';
          showButton.style.padding = '5px 10px';
          showButton.style.border = 'none';
          showButton.style.borderRadius = '4px';
          showButton.style.cursor = 'pointer';
          
          // 添加Show按钮点击事件
          showButton.addEventListener('click', () => {
            // 简单地高亮显示标签页，不切换窗口焦点
            chrome.tabs.highlight({
              tabs: [tab.index],
              windowId: tab.windowId
            });
          });
          
          // 添加Close按钮，点击后关闭对应标签页
          const closeButton = document.createElement('button');
          closeButton.className = 'close-tab-btn';
          closeButton.textContent = 'Close';
          closeButton.style.marginLeft = '5px';
          closeButton.style.backgroundColor = '#F44336';
          closeButton.style.color = 'white';
          closeButton.style.padding = '5px 10px';
          closeButton.style.border = 'none';
          closeButton.style.borderRadius = '4px';
          closeButton.style.cursor = 'pointer';
          
          // 添加Close按钮点击事件
          closeButton.addEventListener('click', () => {
            // 关闭标签页但保持扩展窗口焦点
            chrome.tabs.remove(tab.id, () => {
              // 从DOM中移除该标签项
              tabItem.remove();
            });
          });
          
          // 添加标签建议容器
          const suggestedTagsContainer = document.createElement('div');
          suggestedTagsContainer.className = 'suggested-tags-container';
          suggestedTagsContainer.style.marginTop = '10px';
          
          // 添加标签建议按钮点击事件
          suggestButton.addEventListener('click', async () => {
            // 获取标题作为上下文
            const itemContext = tab.title || '';
            
            // 获取标签建议
            const suggestedTags = await mockGetTagSuggestions(itemContext);
            
            // 清空之前的建议
            suggestedTagsContainer.innerHTML = '';
            
            // 显示新的建议
            if (suggestedTags && suggestedTags.length > 0) {
              suggestedTags.forEach(tagText => {
                const tagElement = createTagElement(tagText, tab.id, tabTags, tagContainer);
                suggestedTagsContainer.appendChild(tagElement);
              });
            } else {
              suggestedTagsContainer.textContent = '没有找到合适的标签建议。';
            }
          });
          
          // Populate existing categories
          chrome.storage.sync.get(['categories'], async (result) => { // Make this callback async
            let categories = result.categories || [];

            // --- BEGIN: Load categories from Catai bookmark folder --- 
            try {
              const cataiFolders = await new Promise(resolve => {
                chrome.bookmarks.search({title: 'Catai'}, cataiMainFolderResults => {
                  if (cataiMainFolderResults && cataiMainFolderResults.length > 0) {
                    chrome.bookmarks.getChildren(cataiMainFolderResults[0].id, children => {
                      resolve(children.filter(child => !child.url)); // Filter out bookmarks, keep folders
                    });
                  } else {
                    resolve([]);
                  }
                });
              });

              cataiFolders.forEach(folder => {
                if (folder.title && !categories.includes(folder.title)) {
                  categories.push(folder.title);
                }
              });
              // Update storage if new categories were added from bookmarks
              await new Promise(resolve => chrome.storage.sync.set({ categories }, resolve)); 

            } catch (error) {
              console.error("Error loading categories from bookmarks:", error);
            }
            // --- END: Load categories from Catai bookmark folder --- 

            // 添加一个空选项
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '-- Select Category --';
            categorySelect.appendChild(emptyOption);
            
            categories.forEach(category => {
              const option = document.createElement('option');
              option.value = category;
              option.textContent = category;
              categorySelect.appendChild(option);
            });
            
            // 如果MongoDB中存在记录且有category，则设置为选中状态
            if (existingData && existingData.category) {
              // 如果categories中不包含该category，则添加
              if (!categories.includes(existingData.category)) {
                const option = document.createElement('option');
                option.value = existingData.category;
                option.textContent = existingData.category;
                categorySelect.appendChild(option);
                
                // 更新本地存储的categories
                categories.push(existingData.category);
                chrome.storage.sync.set({ categories });
              }
              
              categorySelect.value = existingData.category;
            }
            
            // Add category button click handler
            addCategoryButton.addEventListener('click', () => {
              const newCategory = newCategoryInput.value.trim();
              if (newCategory) {
                if (!categories.includes(newCategory)) {
                  categories.push(newCategory);
                  chrome.storage.sync.set({ categories }, () => {
                    // Add to current dropdown
                    const option = document.createElement('option');
                    option.value = newCategory;
                    option.textContent = newCategory;
                    categorySelect.appendChild(option);
                    
                    // Add to all other dropdowns
                    const allSelects = document.querySelectorAll('select.tag-input');
                    allSelects.forEach(select => {
                      if (select !== categorySelect) {
                        const newOption = document.createElement('option');
                        newOption.value = newCategory;
                        newOption.textContent = newCategory;
                        select.appendChild(newOption);
                      }
                    });
                    
                    // Set the new category as selected
                    categorySelect.value = newCategory;
                    
                    newCategoryInput.value = '';
                    alert(`Category "${newCategory}" added successfully!`);
                  });
                } else {
                  alert(`Category "${newCategory}" already exists!`);
                }
              } else {
                alert('Please enter a valid category name!');
              }
            });
          });
          
          // Create container for Urgency and Importance on same line
          const priorityContainer = document.createElement('div');
          priorityContainer.style.display = 'flex';
          priorityContainer.style.alignItems = 'center';
          priorityContainer.style.marginTop = '10px';
          priorityContainer.style.gap = '15px';
          
          if (!isLoggedIn) {
            priorityContainer.classList.add('login-required-field-container');
          }
          
          // Add Urgency dropdown (1-5)
          const urgencyContainer = document.createElement('div');
          urgencyContainer.style.display = 'flex';
          urgencyContainer.style.alignItems = 'center';
          
          const urgencyLabel = document.createElement('label');
          urgencyLabel.textContent = 'Urgency:';
          urgencyLabel.className = 'urgency-label';
          urgencyLabel.style.fontWeight = 'bold';
          urgencyLabel.style.marginRight = '5px';
          
          const urgencySelect = document.createElement('select');
          urgencySelect.className = 'tag-input';
          urgencySelect.style.width = '50px';
          urgencySelect.setAttribute('data-urgency-tab-id', tab.id);
          
          // Add options 1-5
          for (let i = 1; i <= 5; i++) {
            const option = document.createElement('option');
            option.value = i;
            option.textContent = i;
            urgencySelect.appendChild(option);
          }
          
          // Set default value to 2
          urgencySelect.value = '2';
          
          // Set value from MongoDB if exists
          if (existingData && existingData.urgency) {
            urgencySelect.value = existingData.urgency;
          }
          
          // Disable if not logged in
          if (!isLoggedIn) {
            urgencySelect.disabled = true;
            urgencySelect.readOnly = true;
            urgencySelect.classList.add('login-required-field');
            
            // Disable all options
            Array.from(urgencySelect.options).forEach(option => {
              option.disabled = true;
            });
            
            urgencyLabel.style.color = "#999999";
            urgencyLabel.style.opacity = "0.6";
            
            // Add event listeners to prevent interaction
            urgencySelect.addEventListener('click', function(e) {
              if (!window.isUserLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                alert('Please login to use this feature');
                return false;
              }
            });
            
            urgencySelect.addEventListener('focus', function(e) {
              if (!window.isUserLoggedIn) {
                e.preventDefault();
                this.blur();
                return false;
              }
            });
          }
          
          urgencyContainer.appendChild(urgencyLabel);
          urgencyContainer.appendChild(urgencySelect);
          
          // Add Importance dropdown (1-5) with similar approach
          const importanceContainer = document.createElement('div');
          importanceContainer.style.display = 'flex';
          importanceContainer.style.alignItems = 'center';
          
          const importanceLabel = document.createElement('label');
          importanceLabel.textContent = 'Importance:';
          importanceLabel.className = 'importance-label';
          importanceLabel.style.fontWeight = 'bold';
          importanceLabel.style.marginRight = '5px';
          
          const importanceSelect = document.createElement('select');
          importanceSelect.className = 'tag-input';
          importanceSelect.style.width = '50px';
          importanceSelect.setAttribute('data-importance-tab-id', tab.id);
          
          // Add options 1-5
          for (let i = 1; i <= 5; i++) {
            const option = document.createElement('option');
            option.value = i;
            option.textContent = i;
            importanceSelect.appendChild(option);
          }
          
          // Set default value to 2
          importanceSelect.value = '2';
          
          // Set value from MongoDB if exists
          if (existingData && existingData.importance) {
            importanceSelect.value = existingData.importance;
          }
          
          // Disable if not logged in
          if (!isLoggedIn) {
            importanceSelect.disabled = true;
            importanceSelect.readOnly = true;
            importanceSelect.classList.add('login-required-field');
            
            // Disable all options
            Array.from(importanceSelect.options).forEach(option => {
              option.disabled = true;
            });
            
            importanceLabel.style.color = "#999999";
            importanceLabel.style.opacity = "0.6";
            
            // Add event listeners to prevent interaction
            importanceSelect.addEventListener('click', function(e) {
              if (!window.isUserLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                alert('Please login to use this feature');
                return false;
              }
            });
            
            importanceSelect.addEventListener('focus', function(e) {
              if (!window.isUserLoggedIn) {
                e.preventDefault();
                this.blur();
                return false;
              }
            });
          }
          
          importanceContainer.appendChild(importanceLabel);
          importanceContainer.appendChild(importanceSelect);
          
          // Add both to the priority container
          priorityContainer.appendChild(urgencyContainer);
          priorityContainer.appendChild(importanceContainer);
          
          // Add Reminder date input with similar approach
          const reminderContainer = document.createElement('div');
          reminderContainer.style.marginTop = '10px';
          
          if (!isLoggedIn) {
            reminderContainer.classList.add('login-required-field-container');
          }
          
          const reminderLabel = document.createElement('div');
          reminderLabel.textContent = 'Reminder Date:';
          reminderLabel.className = 'reminder-label';
          reminderLabel.style.fontWeight = 'bold';
          
          const reminderInput = document.createElement('input');
          reminderInput.type = 'date';
          reminderInput.className = 'tag-input';
          reminderInput.setAttribute('data-reminder-tab-id', tab.id);
          
          // Set value from MongoDB if exists
          if (existingData && existingData.reminder) {
            reminderInput.value = existingData.reminder;
          }
          
          // Disable if not logged in
          if (!isLoggedIn) {
            reminderInput.disabled = true;
            reminderInput.readOnly = true;
            reminderInput.classList.add('login-required-field');
            
            reminderLabel.style.color = "#999999";
            reminderLabel.style.opacity = "0.6";
            
            // Add event listeners to prevent interaction
            reminderInput.addEventListener('click', function(e) {
              if (!window.isUserLoggedIn) {
                e.preventDefault();
                e.stopPropagation();
                alert('Please login to use this feature');
                return false;
              }
            });
            
            reminderInput.addEventListener('focus', function(e) {
              if (!window.isUserLoggedIn) {
                e.preventDefault();
                this.blur();
                return false;
              }
            });
          }
          
          reminderContainer.appendChild(reminderLabel);
          reminderContainer.appendChild(reminderInput);
          
          inputGroup.appendChild(categorySelect);
          inputGroup.appendChild(newCategoryInput);
          inputGroup.appendChild(addCategoryButton);
          inputGroup.appendChild(document.createElement('br'));
          
          // Add new fields
          inputGroup.appendChild(priorityContainer);
          inputGroup.appendChild(reminderContainer);
          inputGroup.appendChild(document.createElement('br'));
          
          inputGroup.appendChild(tagInput);
          inputGroup.appendChild(saveButton);
          inputGroup.appendChild(document.createElement('br'));
          inputGroup.appendChild(suggestButton);
          inputGroup.appendChild(showButton);
          inputGroup.appendChild(closeButton);
          
          tabItem.appendChild(titleDiv);
          tabItem.appendChild(urlDiv);
          tabItem.appendChild(tagContainer);
          tabItem.appendChild(inputGroup);
          tabItem.appendChild(suggestedTagsContainer);
          
          tabList.appendChild(tabItem);
        });
      });
    });
  });
  
  /**
   * 获取标签建议的函数
   * @param {string} context - 项目的相关上下文信息
   * @returns {Promise<string[]>} - 返回一个包含建议标签字符串的数组
   */
  async function mockGetTagSuggestions(context) {
    console.log(`正在为上下文获取标签建议: "${context}"`);
    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 直接从标题中提取单词作为标签
    const suggestedTags = [];
    
    // 1. 直接提取标题中的单词或短语
    if (context) {
      // 提取2-4个字符的中文词组
      const words = context.split(/\s+/).filter(word => word.length > 0);
      
      // 最多取4个词
      for (let i = 0; i < Math.min(4, words.length); i++) {
        if (words[i] && !suggestedTags.includes(words[i])) {
          suggestedTags.push(words[i]);
        }
      }
    }
    
    // 2. 如果没有足够的标签，使用一些通用标签
    if (suggestedTags.length === 0) {
      return ["标签1", "标签2", "标签3", "标签4"];
    }
    
    return suggestedTags;
  }
  
  /**
   * 创建一个标签元素（包含文本和删除按钮）
   * @param {string} tagText - 标签的文本内容
   * @param {number} tabId - 标签页ID
   * @param {object} tabTags - 标签页标签对象
   * @param {HTMLElement} tagContainer - 标签容器元素
   * @returns {HTMLElement} - 返回创建的标签元素 (span)
   */
  function createTagElement(tagText, tabId, tabTags, tagContainer) {
    const tagSpan = document.createElement('span');
    tagSpan.classList.add('tag', 'suggested-tag');
    
    const textNode = document.createTextNode(tagText + ' ');
    tagSpan.appendChild(textNode);
    
    // 添加"添加"按钮
    const addButton = document.createElement('button');
    addButton.textContent = '+';
    addButton.style.marginLeft = '5px';
    addButton.style.cursor = 'pointer';
    addButton.style.border = 'none';
    addButton.style.background = 'transparent';
    addButton.style.color = 'green';
    
    addButton.addEventListener('click', () => {
      // 将标签添加到实际标签中
      const currentTags = tabTags[tabId] || [];
      if (!currentTags.includes(tagText)) {
        currentTags.push(tagText);
        tabTags[tabId] = currentTags;
        
        // 保存到存储
        chrome.storage.sync.set({ tabTags }, () => {
          // 添加新标签到显示
          const newTagSpan = document.createElement('span');
          newTagSpan.className = 'tag-display';
          newTagSpan.textContent = tagText;
          tagContainer.appendChild(newTagSpan);
          
          // 移除建议标签
          tagSpan.remove();
        });
      }
    });
    
    // 添加"删除"按钮
    const deleteButton = document.createElement('button');
    deleteButton.classList.add('delete-tag-btn');
    deleteButton.textContent = 'X';
    deleteButton.style.marginLeft = '5px';
    deleteButton.style.cursor = 'pointer';
    deleteButton.style.border = 'none';
    deleteButton.style.background = 'transparent';
    deleteButton.style.color = 'red';
    
    deleteButton.addEventListener('click', () => {
      tagSpan.remove(); // 点击删除按钮时移除该标签
    });
    
    tagSpan.appendChild(addButton);
    tagSpan.appendChild(deleteButton);
    return tagSpan;
  }
  
  // Check authentication status and update UI
  function checkAuthAndUpdateUI() {
    chrome.storage.sync.get(['authUser'], function(result) {
      const authUser = result.authUser;
      const saveToMongoBtn = document.getElementById('saveToMongo');
      const loadCategoriesBtn = document.getElementById('loadCategories');
      const loginNotice = document.getElementById('loginNotice');
      
      if (authUser && authUser.loggedIn) {
        // User is logged in, enable buttons
        saveToMongoBtn.disabled = false;
        loadCategoriesBtn.disabled = false;
        saveToMongoBtn.title = "Save to MongoDB";
        loadCategoriesBtn.title = "Load MongoDB's Category";
        saveToMongoBtn.style.backgroundColor = "#4CAF50";
        saveToMongoBtn.style.cursor = "pointer";
        loadCategoriesBtn.style.backgroundColor = "#2196F3";
        loadCategoriesBtn.style.cursor = "pointer";
        loginNotice.style.display = "none";
      } else {
        // User is not logged in, disable buttons
        saveToMongoBtn.disabled = true;
        loadCategoriesBtn.disabled = true;
        saveToMongoBtn.title = "Please login to use this feature";
        loadCategoriesBtn.title = "Please login to use this feature";
        
        // Add gray style to disabled buttons
        saveToMongoBtn.style.backgroundColor = "#cccccc";
        saveToMongoBtn.style.cursor = "not-allowed";
        loadCategoriesBtn.style.backgroundColor = "#cccccc";
        loadCategoriesBtn.style.cursor = "not-allowed";
        loginNotice.style.display = "block";
      }
    });
  }
});







