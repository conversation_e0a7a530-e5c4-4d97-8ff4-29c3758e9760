// Firefox compatibility layer for Chrome extension APIs
// This file provides compatibility for Chrome APIs in Firefox

// Check if we're in Firefox and browser API is available
if (typeof browser !== 'undefined' && typeof chrome === 'undefined') {
  // Create chrome namespace as alias to browser
  window.chrome = browser;
} else if (typeof browser === 'undefined' && typeof chrome !== 'undefined') {
  // Create browser namespace as alias to chrome (for Chrome)
  window.browser = chrome;
}

// Polyfill for Promise-based APIs in Chrome
if (typeof chrome !== 'undefined' && !chrome.runtime.sendMessage.then) {
  // Wrap Chrome APIs to return Promises
  const promisifyAPI = (api, method) => {
    const original = api[method];
    api[method] = function(...args) {
      return new Promise((resolve, reject) => {
        const callback = (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(result);
          }
        };
        original.call(api, ...args, callback);
      });
    };
  };

  // Promisify common Chrome APIs
  if (chrome.storage && chrome.storage.sync) {
    promisifyAPI(chrome.storage.sync, 'get');
    promisifyAPI(chrome.storage.sync, 'set');
    promisifyAPI(chrome.storage.sync, 'remove');
  }

  if (chrome.tabs) {
    promisifyAPI(chrome.tabs, 'query');
    promisifyAPI(chrome.tabs, 'sendMessage');
    promisifyAPI(chrome.tabs, 'highlight');
    promisifyAPI(chrome.tabs, 'remove');
  }

  if (chrome.windows) {
    promisifyAPI(chrome.windows, 'getCurrent');
  }

  if (chrome.bookmarks) {
    promisifyAPI(chrome.bookmarks, 'search');
    promisifyAPI(chrome.bookmarks, 'create');
    promisifyAPI(chrome.bookmarks, 'getChildren');
    promisifyAPI(chrome.bookmarks, 'get');
  }

  if (chrome.runtime) {
    promisifyAPI(chrome.runtime, 'sendMessage');
  }
}
