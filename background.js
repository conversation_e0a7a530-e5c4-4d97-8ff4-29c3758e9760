// Listen for messages from content scripts or popup
browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'saveToMongoDB') {
    console.log('Received save request:', request.data);

    // Get current profile name and NodeJS server URL from storage
    browser.storage.sync.get(['llmKey', 'nodeJsServer']).then(function(result) {
      const profileName = result.llmKey || 'test';
      const serverUrl = result.nodeJsServer || 'http://localhost:3000';

      // Send data to backend server
      fetch(`${serverUrl}/save-page`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: request.data,
          profileName: profileName
        })
      })
      .then(response => {
        console.log('Save response status:', response.status);
        return response.json();
      })
      .then(result => {
        console.log('Save result:', result);
        sendResponse({ success: true, result });
      })
      .catch(error => {
        console.error('Save error:', error);
        sendResponse({ success: false, error: error.message });
      });
    });
    return true; // Will respond asynchronously
  }

  if (request.action === 'saveToMongoDB2') {
    console.log('Received save request:', request.data);

    // Get current profile name and NodeJS server URL from storage
    browser.storage.sync.get(['llmKey', 'nodeJsServer']).then(function(result) {
      const profileName = result.llmKey || 'test';
      const serverUrl = result.nodeJsServer || 'http://localhost:3000';

      // Send data to backend server
      fetch(`${serverUrl}/cata-save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: request.data,
          profileName: profileName
        })
      })
      .then(response => {
        console.log('Save response status:', response.status);
        return response.json();
      })
      .then(result => {
        console.log('Save result:', result);
        sendResponse({ success: true, result });
      })
      .catch(error => {
        console.error('Save error:', error);
        sendResponse({ success: false, error: error.message });
      });
    });
    return true; // Will respond asynchronously
  }

  if (request.action === 'getCataData') {
    console.log('Received get cata data request');

    // Get current profile name and NodeJS server URL from storage
    browser.storage.sync.get(['llmKey', 'nodeJsServer']).then(function(result) {
      const profileName = result.llmKey || 'test';
      const serverUrl = result.nodeJsServer || 'http://localhost:3000';

      // Get data from backend server
      fetch(`${serverUrl}/get-cata-data?profileName=${profileName}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => {
        console.log('Get response status:', response.status);
        return response.json();
      })
      .then(result => {
        console.log('Get result:', result);
        sendResponse({ success: true, data: result.data });
      })
      .catch(error => {
        console.error('Get error:', error);
        sendResponse({ success: false, error: error.message });
      });
    });
    return true; // Will respond asynchronously
  }
});

async function saveToMongoDB(data) {
  let client;
  try {
    // Get current profile name from storage
    const result = await browser.storage.sync.get(['llmKey', 'mongoUrl']);

    const profileName = result.llmKey || 'test';
    const mongoUrl = result.mongoUrl || 'mongodb://localhost:27017';
    
    client = new MongoClient(mongoUrl);
    await client.connect();
    
    const db = client.db(profileName);
    const collection = db.collection('CataPage');
    
    // Handle single object or array of objects
    const dataArray = Array.isArray(data) ? data : [data];
    
    const results = [];
    for (const item of dataArray) {
      // Check if document exists with same title and URL
      const existingDoc = await collection.findOne({
        title: item.title,
        url: item.url
      });
      
      let result;
      if (existingDoc) {
        // Update existing document
        result = await collection.updateOne(
          { _id: existingDoc._id },
          { $set: {
            ...item,
            timestamp: new Date()
          }}
        );
      } else {
        // Insert new document
        result = await collection.insertOne({
          ...item,
          timestamp: new Date()
        });
      }
      results.push(result);
    }
    
    return results;
  } finally {
    if (client) {
      await client.close();
    }
  }
}