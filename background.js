// Listen for messages from content scripts or popup
browser.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  if (request.action === 'saveToMongoDB') {
    console.log('Received save request:', request.data);

    try {
      // Get current profile name and NodeJS server URL from storage
      const result = await browser.storage.sync.get(['llmKey', 'nodeJsServer']);
      const profileName = result.llmKey || 'test';
      const serverUrl = result.nodeJsServer || 'http://localhost:3000';

      // Send data to backend server
      const response = await fetch(`${serverUrl}/save-page`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: request.data,
          profileName: profileName
        })
      });

      console.log('Save response status:', response.status);
      const responseData = await response.json();
      console.log('Save result:', responseData);

      return { success: true, result: responseData };
    } catch (error) {
      console.error('Save error:', error);
      return { success: false, error: error.message };
    }
  }

  if (request.action === 'saveToMongoDB2') {
    console.log('Received save request:', request.data);

    try {
      // Get current profile name and NodeJS server URL from storage
      const result = await browser.storage.sync.get(['llmKey', 'nodeJsServer']);
      const profileName = result.llmKey || 'test';
      const serverUrl = result.nodeJsServer || 'http://localhost:3000';

      // Send data to backend server
      const response = await fetch(`${serverUrl}/cata-save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          data: request.data,
          profileName: profileName
        })
      });

      console.log('Save response status:', response.status);
      const responseData = await response.json();
      console.log('Save result:', responseData);

      return { success: true, result: responseData };
    } catch (error) {
      console.error('Save error:', error);
      return { success: false, error: error.message };
    }
  }

  if (request.action === 'getCataData') {
    console.log('Received get cata data request');

    try {
      // Get current profile name and NodeJS server URL from storage
      const result = await browser.storage.sync.get(['llmKey', 'nodeJsServer']);
      const profileName = result.llmKey || 'test';
      const serverUrl = result.nodeJsServer || 'http://localhost:3000';

      // Get data from backend server
      const response = await fetch(`${serverUrl}/get-cata-data?profileName=${profileName}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('Get response status:', response.status);
      const responseData = await response.json();
      console.log('Get result:', responseData);

      return { success: true, data: responseData.data };
    } catch (error) {
      console.error('Get error:', error);
      return { success: false, error: error.message };
    }
  }
});

async function saveToMongoDB(data) {
  let client;
  try {
    // Get current profile name from storage
    const result = await browser.storage.sync.get(['llmKey', 'mongoUrl']);

    const profileName = result.llmKey || 'test';
    const mongoUrl = result.mongoUrl || 'mongodb://localhost:27017';
    
    client = new MongoClient(mongoUrl);
    await client.connect();
    
    const db = client.db(profileName);
    const collection = db.collection('CataPage');
    
    // Handle single object or array of objects
    const dataArray = Array.isArray(data) ? data : [data];
    
    const results = [];
    for (const item of dataArray) {
      // Check if document exists with same title and URL
      const existingDoc = await collection.findOne({
        title: item.title,
        url: item.url
      });
      
      let result;
      if (existingDoc) {
        // Update existing document
        result = await collection.updateOne(
          { _id: existingDoc._id },
          { $set: {
            ...item,
            timestamp: new Date()
          }}
        );
      } else {
        // Insert new document
        result = await collection.insertOne({
          ...item,
          timestamp: new Date()
        });
      }
      results.push(result);
    }
    
    return results;
  } finally {
    if (client) {
      await client.close();
    }
  }
}