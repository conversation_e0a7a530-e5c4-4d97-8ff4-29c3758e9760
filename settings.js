document.addEventListener('DOMContentLoaded', function() {
  // Check authentication status
  checkAuthStatus();
  
  // Load saved settings
  chrome.storage.sync.get(['llmKey', 'baseUrl', 'token', 'model', 'prompt', 'mongoUrl', 'llmProfiles', 'nodeJsServer', 'authUser'], function(result) {
    
    // Handle New Profile button click
    document.getElementById('newProfileButton').addEventListener('click', function() {
      document.getElementById('newProfileName').value = 'test';
    });
    
    // Handle Save Profile button click
    document.getElementById('saveProfileButton').addEventListener('click', function() {
      const profileName = document.getElementById('newProfileName').value.trim() || 'test';
      
      const currentProfile = {
        name: profileName,
        llmKey: document.getElementById('llmKey').value,
        baseUrl: document.getElementById('baseUrl').value,
        token: document.getElementById('token').value,
        model: document.getElementById('model').value,
        prompt: document.getElementById('prompt').value,
        mongoUrl: document.getElementById('mongoUrl').value,
        nodeJsServer: document.getElementById('nodeJsServer').value
      };
      
      // Get existing profiles or create new array
      const profiles = result.llmProfiles || [];
      
      // Check if profile name exists
      if (profiles.some(p => p.name === profileName)) {
        if (!confirm(`Profile "${profileName}" already exists. Overwrite?`)) return;
        // Remove existing profile with same name
        profiles = profiles.filter(p => p.name !== profileName);
      }
      
      // Add new profile
      profiles.push(currentProfile);
      
      // Save updated profiles
      chrome.storage.sync.set({
        llmProfiles: profiles
      }, function() {
        alert(`Profile "${profileName}" saved successfully!`);
        // Refresh profile dropdown
        const profileSelect = document.getElementById('llmKey');
        profileSelect.innerHTML = '<option value="">Select a profile</option>';
        profiles.forEach(profile => {
          const option = document.createElement('option');
          option.value = profile.name;
          option.textContent = profile.name;
          profileSelect.appendChild(option);
        });
        // Select the newly saved profile
        document.getElementById('llmKey').value = profileName;
      });
    });
    // Load profiles if they exist
    if (result.llmProfiles) {
      const profileSelect = document.getElementById('llmKey');
      profileSelect.innerHTML = '<option value="">Select a profile</option>';
      
      result.llmProfiles.forEach(profile => {
        const option = document.createElement('option');
        option.value = profile.name;
        option.textContent = profile.name;
        profileSelect.appendChild(option);
      });
      
      // Add change event listener to profile dropdown
      profileSelect.addEventListener('change', function() {
        const selectedProfile = result.llmProfiles.find(p => p.name === this.value);
        if (selectedProfile) {
          // Load selected profile settings into form
          document.getElementById('baseUrl').value = selectedProfile.baseUrl || '';
          document.getElementById('token').value = selectedProfile.token || '';
          document.getElementById('model').value = selectedProfile.model || '';
          document.getElementById('prompt').value = selectedProfile.prompt || '';
          document.getElementById('mongoUrl').value = selectedProfile.mongoUrl || 'mongodb://localhost:27017';
          document.getElementById('nodeJsServer').value = selectedProfile.nodeJsServer || 'http://localhost:3000';
        }
      });
    }
    
    if (result.llmKey) {
      document.getElementById('llmKey').value = result.llmKey;
      
      // Load the selected profile's settings
      const selectedProfile = result.llmProfiles?.find(p => p.name === result.llmKey);
      if (selectedProfile) {
        document.getElementById('baseUrl').value = selectedProfile.baseUrl || '';
        document.getElementById('token').value = selectedProfile.token || '';
        document.getElementById('model').value = selectedProfile.model || '';
        document.getElementById('prompt').value = selectedProfile.prompt || '';
        document.getElementById('mongoUrl').value = selectedProfile.mongoUrl || 'mongodb://localhost:27017';
        document.getElementById('nodeJsServer').value = selectedProfile.nodeJsServer || 'http://localhost:3000';
      } else {
        // Fall back to individual settings if profile not found
        if (result.baseUrl) {
          document.getElementById('baseUrl').value = result.baseUrl;
        }
        if (result.token) {
          document.getElementById('token').value = result.token;
        }
        if (result.model) {
          document.getElementById('model').value = result.model;
        }
        if (result.prompt) {
          document.getElementById('prompt').value = result.prompt;
        }
      }
    } else {
      // No profile selected, use individual settings
      if (result.baseUrl) {
        document.getElementById('baseUrl').value = result.baseUrl;
      }
      if (result.token) {
        document.getElementById('token').value = result.token;
      }
      if (result.model) {
        document.getElementById('model').value = result.model;
      }
      if (result.prompt) {
        document.getElementById('prompt').value = result.prompt;
      }
    }
    // Set default NodeJS server URL if not already set
    document.getElementById('nodeJsServer').value = result.nodeJsServer || 'http://localhost:3000';
    // Set default MongoDB URL if not already set
    document.getElementById('mongoUrl').value = result.mongoUrl || 'mongodb://localhost:27017';
  });

  // Handle Apply button click
  document.getElementById('applyButton').addEventListener('click', function() {
    const llmKey = document.getElementById('llmKey').value;
    const baseUrl = document.getElementById('baseUrl').value;
    const token = document.getElementById('token').value;
    const model = document.getElementById('model').value;
    const prompt = document.getElementById('prompt').value;
    const mongoUrl = document.getElementById('mongoUrl').value;
    const nodeJsServer = document.getElementById('nodeJsServer').value;

    // Get profiles to update the current profile
    chrome.storage.sync.get(['llmProfiles'], function(result) {
      const profiles = result.llmProfiles || [];
      const currentProfileIndex = profiles.findIndex(p => p.name === llmKey);
      
      // Update profile if it exists
      if (currentProfileIndex >= 0) {
        profiles[currentProfileIndex] = {
          name: llmKey,
          baseUrl: baseUrl,
          token: token,
          model: model,
          prompt: prompt,
          mongoUrl: mongoUrl,
          nodeJsServer: nodeJsServer
        };
        
        // Save updated profiles
        chrome.storage.sync.set({
          llmProfiles: profiles,
          llmKey: llmKey,
          baseUrl: baseUrl,
          token: token,
          model: model,
          prompt: prompt,
          mongoUrl: mongoUrl,
          nodeJsServer: nodeJsServer
        }, function() {
          alert('Settings saved successfully!');
        });
      } else {
        // Just save current settings if profile doesn't exist
        chrome.storage.sync.set({
          llmKey: llmKey,
          baseUrl: baseUrl,
          token: token,
          model: model,
          prompt: prompt,
          mongoUrl: mongoUrl,
          nodeJsServer: nodeJsServer
        }, function() {
          alert('Settings saved successfully!');
        });
      }
    });
  });

  // Handle Back button click
  document.getElementById('backButton').addEventListener('click', function() {
    window.location.href = 'popup.html';
  });

  // Handle Test button click
  document.getElementById('testButton').addEventListener('click', async function() {
    const token = document.getElementById('token').value;
    let baseUrl = document.getElementById('baseUrl').value;
    const model = document.getElementById('model').value;
    const prompt = document.getElementById('prompt').value;

    if (!token || !baseUrl || !model || !prompt) {
      alert('Please fill in all required fields before testing.');
      return;
    }

    const modal = document.getElementById('testModal');
    const testResponse = document.getElementById('testResponse');
    modal.style.display = 'block';
    testResponse.textContent = 'Testing connection...';

    try {
      // Clean and validate baseUrl
      baseUrl = baseUrl.trim();
      if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
        baseUrl = 'https://' + baseUrl;
      }
      // Remove trailing slash if present
      baseUrl = baseUrl.replace(/\/$/, '');
      
      // Log the request details for debugging
      console.log('Request URL:', `${baseUrl}/chat/completions`);
      console.log('Model:', model);
      console.log('Prompt:', prompt);

      const options = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: model,
          messages: [{
            role: 'user',
            content: 'Hi, which model are you using?' //prompt.replace('{content}', 'This is a test message.')
          }],
          stream: false,
          max_tokens: 512,
          enable_thinking: false,
          thinking_budget: 4096,
          min_p: 0.05,
          stop: null,
          temperature: 0.7,
          top_p: 0.7,
          top_k: 50,
          frequency_penalty: 0.5,
          n: 1,
          response_format: { type: 'text' },
          tools: [{
            type: 'function',
            function: {
              description: '',
              name: '',
              parameters: {},
              strict: false
            }
          }]
        })
      };

      // Log the request headers (excluding token)
      console.log('Request Headers:', {
        ...options.headers,
        'Authorization': 'Bearer [REDACTED]'
      });

      const response = await fetch(`${baseUrl}/chat/completions`, options);
      const rawResponse = await response.text();
      
      // Log the response status and headers
      console.log('Response Status:', response.status);
      console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
      console.log('Raw Response:', rawResponse);

      if (response.status === 404) {
        throw new Error(`API endpoint not found. Please check if the Base URL is correct.\nCurrent URL: ${baseUrl}/chat/completions`);
      }

      try {
        const data = JSON.parse(rawResponse);
        if (!response.ok) {
          throw new Error(data.error?.message || `API request failed with status ${response.status}`);
        }
        const responseContent = data.choices?.[0]?.message?.content;
        if (!responseContent) {
          throw new Error('No response content in API response');
        }
        testResponse.textContent = responseContent;
      } catch (parseError) {
        testResponse.textContent = `Error parsing response: ${parseError.message}\n\nRaw response:\n${rawResponse}\n\nPlease check:\n1. Base URL is correct\n2. API endpoint is correct\n3. Server is responding with valid JSON`;
      }
    } catch (error) {
      testResponse.textContent = `Error: ${error.message}\n\nPlease check:\n1. Base URL is correct\n2. API endpoint is correct\n3. Server is accessible`;
    }
  });

  // Handle modal close button click
  document.getElementById('closeModal').addEventListener('click', function() {
    document.getElementById('testModal').style.display = 'none';
  });

  // Close modal when clicking outside
  window.addEventListener('click', function(event) {
    const modal = document.getElementById('testModal');
    if (event.target === modal) {
      modal.style.display = 'none';
    }
    
    const loginModal = document.getElementById('loginModal');
    if (event.target === loginModal) {
      loginModal.style.display = 'none';
    }
    
    const signupModal = document.getElementById('signupModal');
    if (event.target === signupModal) {
      signupModal.style.display = 'none';
    }
  });

  // Test MongoDB connection
  document.getElementById('testMongo').addEventListener('click', async () => {
    const mongoUrl = document.getElementById('mongoUrl').value;
    const nodeJsServer = document.getElementById('nodeJsServer').value || 'http://localhost:3000';
    const testResponse = document.getElementById('testResponse');
    const modal = document.getElementById('testModal');
    
    if (!mongoUrl) {
      testResponse.textContent = 'Please enter MongoDB connection URL';
      modal.style.display = 'block';
      return;
    }

    try {
      testResponse.textContent = 'Testing MongoDB connection...';
      modal.style.display = 'block';

      // Make a request to your backend to test MongoDB connection
      const response = await fetch(`${nodeJsServer}/test-mongo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mongoUrl })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        testResponse.textContent = 'MongoDB connection successful!';
      } else {
        throw new Error(data.error || 'Connection failed');
      }
    } catch (error) {
      console.error('Connection test error:', error);
      let errorMessage = 'MongoDB connection failed: ';
      
      if (error.message.includes('Failed to fetch')) {
        errorMessage += 'Backend service is not running. Please start the backend service first.\n\n';
        errorMessage += 'Steps to start backend:\n';
        errorMessage += '1. Open terminal\n';
        errorMessage += '2. cd backend\n';
        errorMessage += '3. npm install\n';
        errorMessage += '4. npm start\n\n';
        errorMessage += 'Then try again.';
      } else {
        errorMessage += error.message;
      }
      
      testResponse.textContent = errorMessage;
    }
  });
  
  // Authentication related code
  // Handle login button click
  document.getElementById('loginButton').addEventListener('click', function() {
    document.getElementById('loginModal').style.display = 'block';
    document.getElementById('loginEmail').value = '<EMAIL>';
    document.getElementById('loginPassword').value = 'api2025#';
  });
  
  // Handle signup button click
  document.getElementById('signupButton').addEventListener('click', function() {
    document.getElementById('signupModal').style.display = 'block';
    document.getElementById('signupEmail').value = '<EMAIL>';
    document.getElementById('signupPassword').value = 'api2025#';
  });
  
  // Handle logout button click
  document.getElementById('logoutButton').addEventListener('click', function() {
    chrome.storage.sync.remove('authUser', function() {
      updateAuthUI(null);
    });
  });
  
  // Close login modal
  document.getElementById('closeLoginModal').addEventListener('click', function() {
    document.getElementById('loginModal').style.display = 'none';
  });
  
  // Close signup modal
  document.getElementById('closeSignupModal').addEventListener('click', function() {
    document.getElementById('signupModal').style.display = 'none';
  });
  
  // Cancel login
  document.getElementById('cancelLogin').addEventListener('click', function() {
    document.getElementById('loginModal').style.display = 'none';
  });
  
  // Cancel signup
  document.getElementById('cancelSignup').addEventListener('click', function() {
    document.getElementById('signupModal').style.display = 'none';
  });
  
  // Submit login
  document.getElementById('submitLogin').addEventListener('click', async function() {
    const email = document.getElementById('loginEmail').value.trim();
    const password = document.getElementById('loginPassword').value;
    
    if (!email) {
      alert('Please enter your email or phone');
      return;
    }
    
    try {
      const nodeJsServer = document.getElementById('nodeJsServer').value || 'http://localhost:3000';
      
      const response = await fetch(`${nodeJsServer}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Save auth user to storage
        const authUser = {
          email,
          loggedIn: true,
          timestamp: new Date().getTime()
        };
        
        chrome.storage.sync.set({ authUser }, function() {
          document.getElementById('loginModal').style.display = 'none';
          updateAuthUI(authUser);
          alert('Logged in successfully!');
        });
      } else {
        alert(`Login failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Login error:', error);
      alert(`Login error: ${error.message}`);
    }
  });
  
  // Submit signup
  document.getElementById('submitSignup').addEventListener('click', async function() {
    const email = document.getElementById('signupEmail').value.trim();
    const password = document.getElementById('signupPassword').value;
    
    if (!email) {
      alert('Please enter your email or phone');
      return;
    }
    
    try {
      const nodeJsServer = document.getElementById('nodeJsServer').value || 'http://localhost:3000';
      
      const response = await fetch(`${nodeJsServer}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      
      if (data.success) {
        // Save auth user to storage
        const authUser = {
          email,
          loggedIn: true,
          timestamp: new Date().getTime()
        };
        
        chrome.storage.sync.set({ authUser }, function() {
          document.getElementById('signupModal').style.display = 'none';
          updateAuthUI(authUser);
          alert('Account created and logged in successfully!');
        });
      } else {
        alert(`Signup failed: ${data.message}`);
      }
    } catch (error) {
      console.error('Signup error:', error);
      alert(`Signup error: ${error.message}`);
    }
  });
});

// Check authentication status
function checkAuthStatus() {
  chrome.storage.sync.get(['authUser'], function(result) {
    const authUser = result.authUser;
    updateAuthUI(authUser);
  });
}

// Update authentication UI
function updateAuthUI(authUser) {
  const authStatus = document.getElementById('authStatus');
  const loginButton = document.getElementById('loginButton');
  const signupButton = document.getElementById('signupButton');
  const logoutButton = document.getElementById('logoutButton');
  
  if (authUser && authUser.loggedIn) {
    authStatus.textContent = `Logged in as: ${authUser.email}`;
    loginButton.style.display = 'none';
    signupButton.style.display = 'none';
    logoutButton.style.display = 'inline-block';
  } else {
    authStatus.textContent = 'Not logged in';
    loginButton.style.display = 'inline-block';
    signupButton.style.display = 'inline-block';
    logoutButton.style.display = 'none';
  }
}